import { useMutation, useQuery } from '@apollo/client';
import { ErrorMessage } from '@hookform/error-message';
import { parse } from 'csv-parse/sync';
import { ChangeEvent, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  BulkMutationResponse,
  CreateBulkProgramReferralInput,
  FeatureName,
  Fund,
  Program,
  ProgramStatus,
  User,
} from '@bybeam/platform-types';
import {
  Button,
  Callout,
  Code,
  Dialog,
  Flex,
  IconButton,
  Quote,
  ScrollArea,
  Select,
  Strong,
  Table,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import { Cross1Icon, ExclamationTriangleIcon, InfoCircledIcon } from '@radix-ui/react-icons';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { programHasPayments } from '@/spa-legacy/utilities/programs';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { sum } from '@/spa-legacy/common/utilities/math';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import pluralize from '@bybeam/platform-lib/format/pluralize';
// import CreateBulkProgramReferralMutation from './CreateBulkProgramReferralMutation.graphql';
// import styles from './BulkProgramReferral.module.css';
// import GetProgramsWithFundDetailsQuery from './GetProgramsWithFundDetailsQuery.graphql';
import { usePostHog } from 'posthog-js/react';

interface Inputs {
  programId: string;
  file: File;
}

const BulkPayments = (): JSX.Element => {
  const MAX_LIMIT = 800;

  const { showSnackbar } = useSnackbar();
  const posthog = usePostHog();
  const [isOpenDialog, setIsOpenDialog] = useState<boolean>(false);
  const [users, setUsers] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedProgram, setSelectedProgram] = useState<
    Program & {
      remainingBalance: number;
      hasLowFunds: boolean;
    }
  >();
  const inputRef = useRef(null);
  const usersCount = users?.length;

  // const { data, loading } = useQuery(GetProgramsWithFundDetailsQuery, {
  //   fetchPolicy: 'cache-first',
  // });

  // const programsWithReferral = useMemo(() => {
  //   const programs = data?.programs?.programs ?? [];
  //   return programs.filter(
  //     (program: Program) =>
  //       program?.status !== ProgramStatus.Closed &&
  //       checkFeature(program?.features ?? [], FeatureName.ProgramsReferral),
  //   );
  // }, [data]);

  // const [
  //   createBulkProgramReferral,
  //   { reset: resetMutation, loading: isMutationLoading, data: mutationData },
  // ] = useMutation<
  //   { programReferral: { createBulkProgramReferral: BulkMutationResponse<User[]> } },
  //   { input: CreateBulkProgramReferralInput }
  // >(CreateBulkProgramReferralMutation);

  const {
    control,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors },
    reset: resetFormState,
    clearErrors,
    setError,
  } = useForm<Inputs>({
    defaultValues: {
      file: undefined,
      programId: undefined,
    },
  });

  const onSubmit = async () => {
    if (!usersCount || !selectedProgram) return;
    try {
      posthog?.capture?.('applicant:bulk_referral_submitted', {
        program_id: selectedProgram.id,
        users_count: usersCount,
      });
      const result = await createBulkProgramReferral({
        variables: {
          input: {
            programId: selectedProgram.id,
            userIds: users.map((row) => row.id),
          },
        },
      });
      if (result?.data?.programReferral?.createBulkProgramReferral?.metadata?.status !== 200) {
        throw new Error('Request failed');
      }
      showSnackbar(
        <>
          <strong>{usersCount}</strong> {pluralize('user has', usersCount, 'users have')} been
          successfully referred to program {selectedProgram.name}.
        </>,
      );
      resetFormState();
      setUsers([]);
      resetMutation();
      setIsOpenDialog(false);
    } catch (e) {
      setError('root', {
        message: `Your request to refer ${usersCount} ${pluralize('user', usersCount)} to
          program ${selectedProgram.name} failed. Please review the information.`,
      });
    }
  };

  const onValidateFile = (file: File, rows: Array<{ id: string }>) => {
    if (!rows?.length || rows.some((row: { id: string }) => !row.id)) {
      return `No user IDs found in ${file.name}.`;
    }
    if (rows.length > MAX_LIMIT)
      return 'File limit reached. Please split your data into multiple files.';
  };

  const onLoadFile = (file: File) => (event: ProgressEvent<FileReader>) => {
    const userKey = 'userId';
    const text = event.target?.result as string;
    const rows = parse(text, {
      columns: (headers) =>
        headers.map((column: string) => {
          const header = column?.toLowerCase();
          if (['userid', 'user id', 'user_id'].includes(header)) return userKey;
          return header;
        }),
      skip_empty_lines: true,
      trim: true,
    });
    const users = rows
      // biome-ignore lint/suspicious/noExplicitAny: report with any type
      ?.map((row: any) => ({
        id: row[userKey],
        name: row.name,
      }))
      .filter(Boolean);

    const error = onValidateFile(file, users);
    if (error) {
      setError('file', { message: error, type: 'validate' });
    } else setUsers(users);
  };

  const onFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    clearErrors('file');
    setUsers([]);

    const file = e.target.files?.[0];
    setValue('file', file as File);

    if (!file) {
      setError('file', {
        type: 'required',
        message: 'File is required.',
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = onLoadFile(file);
    reader.readAsText(file);
  };

  const onProgramChange = (programId: string | undefined): void => {
    const program = programsWithReferral?.find((program: Program) => program?.id === programId);
    if (!program || !program?.funds) {
      setSelectedProgram(undefined);
      return;
    }

    const startingBalance = sum(program.funds.map((fund: Fund) => fund?.startingBalance ?? 0)) || 1;
    const remainingBalance = sum(
      program.funds.map((fund: Fund) => fund?.stats?.remainingBalance ?? 0),
    );
    setSelectedProgram({
      ...program,
      remainingBalance: remainingBalance,
      hasLowFunds: programHasPayments(program) && remainingBalance / startingBalance < 0.05,
    });
  };

  const onValidateProgram = (programId: string) => {
    const program = programsWithReferral?.find((program: Program) => program?.id === programId);
    const remainingFunds = sum(
      (program.funds ?? []).map((fund: Fund) => fund.stats?.remainingBalance ?? 0),
    );
    if (program && programHasPayments(program) && remainingFunds <= 0) {
      return 'There is no fund available';
    }
    return true;
  };

  // const mutationErrors = mutationData?.programReferral?.createBulkProgramReferral?.metadata?.errors;

  return (
    <>
      <div className="p-4 border-t border-tableBorder">
        <div className="mb-2">
          <div className="text-textSecondary">
            <strong>Bulk payments</strong>
          </div>
          <small>Use a file to initiate payments for multiple cases at once.</small>
        </div>
        <Dialog.Root
          onOpenChange={(open) => {
            resetFormState();
            // resetMutation();
            setUsers([]);
            setIsOpenDialog(open);
          }}
          open={isOpenDialog}
        >
          <Dialog.Trigger>
            <Button variant="outline">Get started</Button>
          </Dialog.Trigger>
          <Dialog.Content size="3">
            <Flex justify="between" gap="1">
              <Dialog.Title>Bulk initiate payments with a file</Dialog.Title>
              <Dialog.Close>
                <IconButton variant="ghost" color="gray">
                  <Cross1Icon />
                </IconButton>
              </Dialog.Close>
            </Flex>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Flex direction="column" gap="4">
                <Text>
                  Upload a CSV file containing a list of Fulfillment Ids for bulk processing. The
                  file must include a column labeled <Code>Fulfillment Ids</Code>.
                </Text>
                <Flex direction="column">
                  <label htmlFor="user-id-file-upload">
                    <Flex gap="2" align="center">
                      <Button
                        type="button"
                        onClick={() => inputRef.current?.click()}
                        variant="outline"
                      >
                        Choose File
                      </Button>
                      <Text size="2" truncate>
                        {getValues('file')?.name || 'No file selected'}
                      </Text>
                    </Flex>
                    <input
                      ref={inputRef}
                      type="file"
                      id="fulfillment-id-file-upload"
                      aria-describedby="file-limits, error-file"
                      aria-invalid={!!errors.file}
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      onChange={onFileChange}
                      hidden
                    />
                  </label>
                  <ErrorMessage
                    errors={errors}
                    name="file"
                    render={({ message }) => (
                      <Text color="red" size="1" id="error-file">
                        {message}
                      </Text>
                    )}
                  />
                  <Text size="1" id="file-limits">
                    There is a limit of {MAX_LIMIT} users per file.
                  </Text>
                </Flex>
                {!!usersCount && (
                  <output htmlFor="user-id-file-upload">
                    <ScrollArea type="hover" scrollbars="vertical">
                      <Table.Root variant="surface">
                        <Table.Header>
                          <Table.Row>
                            <Table.ColumnHeaderCell>User ID</Table.ColumnHeaderCell>
                            <Table.ColumnHeaderCell>Name</Table.ColumnHeaderCell>
                          </Table.Row>
                        </Table.Header>
                        <Table.Body>
                          {users.map((row) => {
                            // const error = mutationErrors?.find((error) => error.id === row.id);
                            return (
                              <Table.Row key={row.id}>
                                <Table.Cell>
                                  <Flex gapX="2" align="center">
                                    <Text aria-describedby={`${row.id}-error`}>{row.id}</Text>
                                    {!!error && (
                                      <Tooltip content={error.message}>
                                        <InfoCircledIcon color="red" id={`${row.id}-error`} />
                                      </Tooltip>
                                    )}
                                  </Flex>
                                </Table.Cell>
                                <Table.Cell>{row.name}</Table.Cell>
                              </Table.Row>
                            );
                          })}
                        </Table.Body>
                      </Table.Root>
                    </ScrollArea>
                    <Text size="1">
                      Found{' '}
                      <Strong>
                        {usersCount} {pluralize('user ID', usersCount)}
                      </Strong>{' '}
                      in <Quote>{getValues('file')?.name}</Quote>
                    </Text>
                  </output>
                )}

                {selectedProgram?.hasLowFunds && (
                  <Callout.Root highContrast color="red" role="alert">
                    <Callout.Icon>
                      <ExclamationTriangleIcon />
                    </Callout.Icon>
                    <Callout.Text>
                      Remaining funds for {selectedProgram?.name} are low at{' '}
                      <Strong>{formatCurrency(selectedProgram?.remainingBalance, true)}</Strong>
                    </Callout.Text>
                  </Callout.Root>
                )}
                <Callout.Root highContrast>
                  <Callout.Icon>
                    <InfoCircledIcon />
                  </Callout.Icon>
                  <Callout.Text>
                    Applicants will receive a notification letting them know about the referral.
                  </Callout.Text>
                </Callout.Root>
                {!!errors?.root && (
                  <Callout.Root highContrast color="red" role="alert">
                    <Callout.Icon>
                      <InfoCircledIcon />
                    </Callout.Icon>
                    <Callout.Text>
                      <ErrorMessage errors={errors} name="root" />
                    </Callout.Text>
                  </Callout.Root>
                )}
              </Flex>
              <Flex gap="3" mt="4" justify="end">
                <Dialog.Close>
                  <Button variant="soft" color="gray">
                    Cancel
                  </Button>
                </Dialog.Close>
                <Button disabled={!users?.length} type="submit">
                  Send Referral
                </Button>
              </Flex>
            </form>
          </Dialog.Content>
        </Dialog.Root>
      </div>
    </>
  );
};

export default BulkPayments;
